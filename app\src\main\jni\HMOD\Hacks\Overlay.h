#pragma once

#include <map>
#include <mutex>
#include <chrono>
#include <vector>
#include <fstream>
#include <string>
#include <iostream>
#include <set>
#include <algorithm>
#include <cmath>

// Cấu trúc lưu trữ dữ liệu kẻ địch cho ESP
struct EnemyData {
    void* enemyObj;
    void* enemyLinker;
    Vector3 position;
    int hp;
    int maxHp;
    bool isVisible;
    bool isDead;
    int objId;         // Thêm objId để dễ dàng xác định
    std::string heroId; // Thêm heroId để lưu trữ
};

// Managers
ESPManager *espManager;
ESPManager *ActorLinker_enemy;

// Global variables for tracking
Array<void *> *m_MiniMapHeroInfos;
std::map<void *, void *> player_mini;
std::map<std::string, GLuint> mapIcon;
std::vector<GLuint> enemyIconTextures;
std::vector<String*> enemyNames;
std::map<int, std::string> objIdToHeroIdMap; // Ánh xạ ObjID -> HeroID
std::map<int, void*> objIdToEnemyLinker;    // Ánh xạ ObjID -> EnemyLinker
std::map<std::string, int> heroIdToObjId;   // Ánh xạ ngược HeroID -> ObjID
struct HeroId {
    void *instance;
    int heroid;
};
ImFont* CN;
std::vector<HeroId *> heroId;

// State tracking variables
bool hasInitializedIcons = false;
static int currentMatchId = 0;
static void* lastMyPlayer = nullptr;
static std::map<int, std::string> playerHeroCache;
static int lastEnemyCount = 0;
static float lastUpdateTime = 0;
static int lastMatchId = -1;
static bool forceUpdate = false;
static bool mappingVerified = false;
float mapNgh = 1.735f;
float radiusMini = 10;
int mimapi = 0;
std::string imagePathBase = "";
//float scaleX;

// Performance optimization variables
static const float ESP_UPDATE_INTERVAL = 0.0f; // ~60 FPS
static const float ICON_UPDATE_INTERVAL = 1.0f; // Update icons every 1s
static const float MAPPING_UPDATE_INTERVAL = 2.0f; // Update mapping every 2s
static const int TEXTURE_COOLDOWN = 1000; // milliseconds
static std::mutex textureMutex;

// Cache dữ liệu kẻ địch
std::vector<EnemyData> enemyCache;
std::mutex cacheMutex;
float lastCacheUpdate = 0.0f;
const float CACHE_UPDATE_INTERVAL = 0.05f; // 20 FPS

// Biến để theo dõi thời gian cập nhật mapping
float lastMappingUpdate = 0.0f;
int mappingUpdateAttempts = 0;
const int MAX_MAPPING_ATTEMPTS = 5;

// Các biến thêm để xử lý trường hợp ESP được bật trước khi vào trận
bool espInitialized = false;
bool matchStarted = false;
std::mutex espStateMutex;
static int initializationAttempts = 0;
static const int MAX_INITIALIZATION_ATTEMPTS = 10;
static float lastInitAttemptTime = 0.0f;
static const float INIT_ATTEMPT_INTERVAL = 1.0f; // 1 giây giữa các lần thử khởi tạo

// Function pointers
String *(*GetHero_Icon)(void *instance, bool bSmall);
VInt3 (*LActorRoot_get_location)(void *instance);
VInt3 (*LActorRoot_get_forward)(void *instance);
uintptr_t (*LActorRoot_LHeroWrapper)(void *instance);
int (*LActorRoot_COM_PLAYERCAMP)(void *instance);
bool (*LActorRoot_get_bActive)(void *instance);
int (*LActorRoot_get_ObjID)(void *instance);
bool (*LObjWrapper_get_IsDeadState)(void *instance);
bool (*LObjWrapper_IsAutoAI)(void *instance);
int (*ValuePropertyComponent_get_actorHp)(void *instance);
int (*ValuePropertyComponent_get_actorHpTotal)(void *instance);
void* (*ValuePropertyComponent_BaseEnergyLogic)(void *instance);

int (*ActorLinker_COM_PLAYERCAMP)(void *instance);
bool (*ActorLinker_IsHostPlayer)(void *instance);
bool (*ActorLinker_IsHostCamp)(void *instance);
int (*ActorLinker_ActorTypeDef)(void *instance);
Vector3 (*ActorLinker_getPosition)(void *instance);
bool (*ActorLinker_get_HPBarVisible)(void *instance);
int (*ActorLinker_get_ObjID)(void *instance);
bool (*ActorLinker_get_bVisible)(void *instance);
uintptr_t (*AsHero)(...);
void (*_SetPlayerName)(...);

// MiniMap functions
int (*MiniMapHeroInfo_get_ObjID)(void *instance);
void (*MiniMapHeroInfo_Show)(void *instance, bool show);
String *(*GetHeroName)(int uuid);
void (*UpdatePosInMiniMap)(void *instance, Vector3 actopos);

// Log utilities for debugging
void LogMessage(const std::string& message) {
    try {
        std::ofstream logFile("/storage/emulated/0/ESP_Log.txt", std::ios::app);
        if (logFile.is_open()) {
            auto now = std::chrono::system_clock::now();
            auto now_c = std::chrono::system_clock::to_time_t(now);
            logFile << std::ctime(&now_c) << ": " << message << std::endl;
            logFile.close();
        }
    } catch(...) {
        // Ignore errors
    }
}

// Kiểm tra xem game đã sẵn sàng để ESP hoạt động chưa
bool IsGameReady() {
    return espManager != NULL &&
           espManager->enemies != NULL &&
           ActorLinker_enemy != NULL &&
           ActorLinker_enemy->enemies != NULL;
}

// Kiểm tra sâu hơn xem trận đấu đã sẵn sàng hoàn toàn chưa
bool IsMatchReady() {
    return IsGameReady() &&
           espManager->MyPlayer != NULL &&
           !espManager->enemies->empty();
}

// Kiểm tra an toàn camera và scene
bool IsCameraAndSceneReady() {
    try {
        Camera* mainCamera = Camera::get_main();
        if (!mainCamera) {
            return false;
        }

        // Kiểm tra xem có đang trong scene game không
        if (!espManager || !espManager->MyPlayer) {
            return false;
        }

        // Thử truy cập vị trí player để đảm bảo game objects hợp lệ
        Vector3 testPos = ActorLinker_getPosition(espManager->MyPlayer);
        return true;
    } catch(...) {
        return false;
    }
}

// Kiểm tra toàn diện trước khi vẽ ESP
bool IsFullyReadyForESP() {
    return IsGameReady() &&
           IsMatchReady() &&
           IsCameraAndSceneReady() &&
           matchStarted;
}

// Utility functions
bool textureExists(const std::string& textureName) {
    return mapIcon.find(textureName) != mapIcon.end();
}

bool fileExists(const std::string& filePath) {
    std::ifstream file(filePath);
    return file.good();
}

// Xóa bộ đệm texture an toàn
void ClearTextureCache() {
    try {
        std::lock_guard<std::mutex> lock(textureMutex);

        for(auto& pair : mapIcon) {
            if(pair.second != 0) {
                glDeleteTextures(1, &pair.second);
            }
        }
        mapIcon.clear();

        for(GLuint textureId : enemyIconTextures) {
            if(textureId != 0) {
                glDeleteTextures(1, &textureId);
            }
        }
        enemyIconTextures.clear();

        playerHeroCache.clear();
        enemyNames.clear();
        objIdToHeroIdMap.clear(); // Xóa mapping
        objIdToEnemyLinker.clear(); // Xóa mapping
        heroIdToObjId.clear();    // Xóa mapping ngược
        hasInitializedIcons = false;
        forceUpdate = false;
        mappingVerified = false;
        mappingUpdateAttempts = 0;

        // Xóa cache
        std::lock_guard<std::mutex> cacheLock(cacheMutex);
        enemyCache.clear();
    } catch(...) {
        // Bỏ qua lỗi
    }
}

const char *GetPack() {
    char *application_id[256];
    FILE *fp = fopen("proc/self/cmdline", "r");
    if (fp) {
        fread(application_id, sizeof(application_id), 1, fp);
        fclose(fp);
    }
    return (const char *) application_id;
}

int dem(int num) {
    int div = 1, num1 = num;
    while (num1 != 0) {
        num1 = num1/10;
        div = div*10;
    }
    return div;
}

Vector3 VInt2Vector(VInt3 location, VInt3 forward) {
    return Vector3(
        (float)(location.X*dem(forward.X)+forward.X)/(1000*dem(forward.X)),
        (float)(location.Y*dem(forward.Y)+forward.Y)/(1000*dem(forward.Y)),
        (float)(location.Z*dem(forward.Z)+forward.Z)/(1000*dem(forward.Z))
    );
}

// Kiểm tra tính hợp lệ của heroId
bool IsValidHeroId(const std::string& heroId) {
    // HeroId thường là 3 ký tự số
    if (heroId.length() != 3) return false;

    // Tất cả ký tự phải là số
    for (char c : heroId) {
        if (!isdigit(c)) return false;
    }

    return true;
}

// Cải tiến LoadTexture để an toàn hơn
GLuint LoadTexture(const char* imagePath) {
    if (!imagePath || strlen(imagePath) == 0) {
        return 0;
    }

    try {
        std::lock_guard<std::mutex> lock(textureMutex);
        std::string pathStr(imagePath);

        static std::map<std::string, std::chrono::steady_clock::time_point> lastLoadTimes;
        auto now = std::chrono::steady_clock::now();

        // Check cooldown
        if(lastLoadTimes.find(pathStr) != lastLoadTimes.end()) {
            if(std::chrono::duration_cast<std::chrono::milliseconds>
               (now - lastLoadTimes[pathStr]).count() < TEXTURE_COOLDOWN) {
                if (textureExists(pathStr)) {
                    return mapIcon[pathStr];
                }
            }
        }

        // Handle force update
        if(forceUpdate) {
            auto it = mapIcon.find(pathStr);
            if(it != mapIcon.end()) {
                if(it->second != 0) {
                    glDeleteTextures(1, &it->second);
                }
                mapIcon.erase(it);
            }
        }

        // Return cached texture if exists
        if (textureExists(pathStr) && !forceUpdate) {
            return mapIcon[pathStr];
        }

        // Check if file exists
        if (!fileExists(pathStr)) {
            return 0;
        }

        // Load new texture
        GLuint textureID = 0;
        glGenTextures(1, &textureID);

        if (textureID == 0) {
            return 0;
        }

        int width, height, channels;
        stbi_set_flip_vertically_on_load(true);
        unsigned char *imageData = stbi_load(imagePath, &width, &height, &channels, STBI_rgb_alpha);

        if (imageData) {
            glBindTexture(GL_TEXTURE_2D, textureID);
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
            glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, imageData);
            glGenerateMipmap(GL_TEXTURE_2D);
            stbi_image_free(imageData);
            glBindTexture(GL_TEXTURE_2D, 0);

            mapIcon[pathStr] = textureID;
            lastLoadTimes[pathStr] = now;
        } else {
            glDeleteTextures(1, &textureID);
            textureID = 0;
        }

        return textureID;
    } catch(...) {
        return 0;
    }
}

// Tìm lỗi khi khởi tạo ESP - Cải tiến an toàn
void InitializeESP() {
    float currentTime = ImGui::GetTime();

    // Hạn chế số lần thử khởi tạo
    if (espInitialized ||
        (initializationAttempts >= MAX_INITIALIZATION_ATTEMPTS && !IsGameReady()) ||
        (currentTime - lastInitAttemptTime < INIT_ATTEMPT_INTERVAL)) {
        return;
    }

    lastInitAttemptTime = currentTime;
    initializationAttempts++;

    // KIỂM TRA AN TOÀN HỚN - Tránh crash khi ở sảnh chờ
    if (!IsGameReady() || !espManager || !espManager->enemies) {
        LogMessage("ESP initialization failed: Game not ready");
        return;
    }

    // Kiểm tra camera và scene trước khi khởi tạo
    if (!IsCameraAndSceneReady()) {
        LogMessage("ESP initialization failed: Camera/Scene not ready");
        return;
    }

    // Kiểm tra thêm: Đảm bảo có ít nhất 1 enemy để tránh crash
    if (espManager->enemies->empty()) {
        LogMessage("ESP initialization delayed: No enemies detected yet");
        return;
    }

    try {
        // Xóa dữ liệu cũ nếu có
        ClearTextureCache();

        // Khởi tạo bộ đệm rỗng
        std::lock_guard<std::mutex> cacheLock(cacheMutex);
        enemyCache.clear();

        espInitialized = true;
        initializationAttempts = 0; // Reset counter
        LogMessage("ESP initialized successfully");
    } catch (...) {
        espInitialized = false;
        LogMessage("Failed to initialize ESP - Exception caught");
    }
}

// Kiểm tra trận đấu mới
void CheckForNewMatch() {
    // Kiểm tra cơ bản xem ESP đã được khởi tạo chưa
    if (!espInitialized) {
        return;
    }

    try {
        // Kiểm tra espManager và enemies có tồn tại không
        if (!espManager || !espManager->enemies) {
            matchStarted = false;
            return;
        }

        static int lastEnemyCount = 0;
        static bool wasEmpty = true;

        int currentEnemyCount = espManager->enemies->size();

        // Phát hiện khi số lượng kẻ địch tăng từ 0 lên > 0
        if (currentEnemyCount > 0 && lastEnemyCount == 0 && wasEmpty) {
            wasEmpty = false;
            matchStarted = true;

            // Xóa sạch dữ liệu
            ClearTextureCache();
            LogMessage("New match detected, cleared cache");
        }

        // Phát hiện kết thúc trận đấu
        if (currentEnemyCount == 0 && lastEnemyCount > 0) {
            wasEmpty = true;
            matchStarted = false;
            ClearTextureCache();
            LogMessage("Match ended, cleared cache");
        }

        lastEnemyCount = currentEnemyCount;
    } catch(...) {
        // Bỏ qua lỗi
    }
}

// Cập nhật ánh xạ ObjID -> HeroID
bool UpdateHeroMapping() {
    if (!IsGameReady() || !matchStarted) {
        return false;
    }

    float currentTime = ImGui::GetTime();
    if (mappingVerified && currentTime - lastMappingUpdate < MAPPING_UPDATE_INTERVAL) {
        return true; // Mapping đã được xác minh và chưa đến thời gian cập nhật
    }

    lastMappingUpdate = currentTime;

    try {
        // Tạo ánh xạ mới
        std::map<int, std::string> newObjIdToHeroId;
        std::map<int, void*> newObjIdToEnemyLinker;
        std::map<std::string, int> newHeroIdToObjId;

        if (!ActorLinker_enemy || !ActorLinker_enemy->enemies) {
            return false;
        }

        int validCount = 0;
        std::string baseImagePath = std::string("/storage/emulated/0/Android/data/") +
                                  GetPack() +
                                  "/files/TH/";

        // Lấy mapping từ tất cả kẻ địch
        for (int i = 0; i < ActorLinker_enemy->enemies->size(); i++) {
            void* EnemyLinker = (*ActorLinker_enemy->enemies)[i]->object;
            if (!EnemyLinker) continue;

            try {
                int objId = ActorLinker_get_ObjID(EnemyLinker);
                String* heroIcon = GetHero_Icon(EnemyLinker, true);

                if (heroIcon) {
                    std::string iconPath = heroIcon->CString();
                    if (!iconPath.empty()) {
                        size_t lastSlash = iconPath.rfind('/');
                        if (lastSlash != std::string::npos) {
                            std::string heroId = iconPath.substr(lastSlash + 1).substr(2,3);

                            if (IsValidHeroId(heroId)) {
                                // Kiểm tra xem file ảnh có tồn tại không
                                std::string imagePath = baseImagePath + heroId + ".png";
                                if (fileExists(imagePath)) {
                                    newObjIdToHeroId[objId] = heroId;
                                    newObjIdToEnemyLinker[objId] = EnemyLinker;
                                    newHeroIdToObjId[heroId] = objId;
                                    validCount++;
                                }
                            }
                        }
                    }
                }
            } catch(...) {
                continue;
            }
        }

        // Kiểm tra tính hợp lệ của mapping
        bool isValid = validCount >= 1; // Ít nhất phải có 1 đối tượng hợp lệ

        // Kiểm tra xem mỗi heroId chỉ xuất hiện một lần
        std::vector<std::string> uniqueHeroIds;
        for (const auto& pair : newObjIdToHeroId) {
            bool found = false;
            for (const auto& heroId : uniqueHeroIds) {
                if (heroId == pair.second) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                uniqueHeroIds.push_back(pair.second);
            }
        }

        // Phát hiện và sửa heroId trùng lặp (nếu có)
        if (uniqueHeroIds.size() != newObjIdToHeroId.size()) {
            // Tìm các heroId bị trùng
            std::map<std::string, std::vector<int>> heroIdDuplicates;
            for (const auto& pair : newObjIdToHeroId) {
                heroIdDuplicates[pair.second].push_back(pair.first);
            }

            // Xử lý các heroId bị trùng
            for (const auto& dupPair : heroIdDuplicates) {
                if (dupPair.second.size() > 1) {
                    // Giữ lại mapping đầu tiên, đánh dấu cần update các mapping khác
                    LogMessage("Found duplicate heroId: " + dupPair.first);

                    // Chỉ giữ lại mapping đầu tiên
                    for (size_t i = 1; i < dupPair.second.size(); i++) {
                        newObjIdToHeroId.erase(dupPair.second[i]);
                    }
                }
            }
        }

        // Nếu mapping hợp lệ, cập nhật vào biến toàn cục
        if (isValid) {
            objIdToHeroIdMap = newObjIdToHeroId;
            objIdToEnemyLinker = newObjIdToEnemyLinker;
            heroIdToObjId = newHeroIdToObjId;
            mappingVerified = true;
            mappingUpdateAttempts = 0;

            std::stringstream ss;
            ss << "Updated hero mapping with " << objIdToHeroIdMap.size() << " heroes";
            LogMessage(ss.str());

            return true;
        } else {
            // Nếu mapping không hợp lệ, tăng số lần thử
            mappingUpdateAttempts++;

            // Nếu đã thử nhiều lần mà không thành công, sử dụng mapping hiện tại
            if (mappingUpdateAttempts >= MAX_MAPPING_ATTEMPTS && !objIdToHeroIdMap.empty()) {
                LogMessage("Using existing mapping after multiple failed attempts");
                return true;
            }

            LogMessage("Failed to create valid hero mapping, attempt " + std::to_string(mappingUpdateAttempts));
            return false;
        }
    } catch(...) {
        LogMessage("Exception in UpdateHeroMapping");
        return false;
    }
}

// Hàm cập nhật cache kẻ địch
void UpdateEnemyCache() {
    // Kiểm tra cơ bản xem ESP đã được khởi tạo và trận đấu đã bắt đầu chưa
    if (!espInitialized || !matchStarted) {
        return;
    }

    float currentTime = ImGui::GetTime();

    // Giới hạn tần suất cập nhật cache
    if (currentTime - lastCacheUpdate < CACHE_UPDATE_INTERVAL) {
        return;
    }
    lastCacheUpdate = currentTime;

    try {
        // Kiểm tra điều kiện cơ bản
        if (!Config.ESPMenu.Enable_ESP ||
            !espManager ||
            !espManager->enemies ||
            espManager->enemies->empty()) {
            std::lock_guard<std::mutex> lock(cacheMutex);
            enemyCache.clear();
            return;
        }

        // Cập nhật mapping trước khi cập nhật cache
        UpdateHeroMapping();

        // Chuẩn bị dữ liệu mới
        std::vector<EnemyData> newCache;

        // Thu thập thông tin kẻ địch
        for (int i = 0; i < espManager->enemies->size(); i++) {
            if (i >= espManager->enemies->size()) {
                break; // Phòng trường hợp size thay đổi giữa vòng lặp
            }

            void* Enemy = (*espManager->enemies)[i]->object;
            void* EnemyLinker = nullptr;

            // Kiểm tra Enemy có tồn tại không
            if (!Enemy) continue;

            // Tìm EnemyLinker tương ứng
            if (ActorLinker_enemy &&
                ActorLinker_enemy->enemies &&
                i < ActorLinker_enemy->enemies->size()) {
                EnemyLinker = (*ActorLinker_enemy->enemies)[i]->object;
            }

            if (!EnemyLinker) continue;

            // Lấy thông tin từ LObjWrapper
            void* LObjWrapper = *(void**)((uint64_t)Enemy +
                IL2Cpp::Il2CppGetFieldOffset("Project.Plugins_d.dll", "NucleusDrive.Logic", "LActorRoot", "ActorControl"));

            if (!LObjWrapper) continue;

            // Lấy thông tin sức khỏe
            void* ValuePropertyComponent = *(void**)((uint64_t)Enemy +
                IL2Cpp::Il2CppGetFieldOffset("Project.Plugins_d.dll", "NucleusDrive.Logic", "LActorRoot", "ValueComponent"));

            if (!ValuePropertyComponent) continue;

            // Tạo cache dữ liệu mới
            EnemyData enemyData;
            enemyData.enemyObj = Enemy;
            enemyData.enemyLinker = EnemyLinker;

            try {
                // Các hàm này có thể gây lỗi nếu đối tượng không hợp lệ
                enemyData.position = VInt2Vector(LActorRoot_get_location(Enemy), LActorRoot_get_forward(Enemy));
                enemyData.hp = ValuePropertyComponent_get_actorHp(ValuePropertyComponent);
                enemyData.maxHp = ValuePropertyComponent_get_actorHpTotal(ValuePropertyComponent);
                enemyData.isVisible = ActorLinker_get_bVisible(EnemyLinker);
                enemyData.isDead = LObjWrapper_get_IsDeadState(LObjWrapper);
                enemyData.objId = ActorLinker_get_ObjID(EnemyLinker);

                // Lấy heroId từ mapping nếu có
                if (objIdToHeroIdMap.find(enemyData.objId) != objIdToHeroIdMap.end()) {
                    enemyData.heroId = objIdToHeroIdMap[enemyData.objId];
                }
            } catch(...) {
                // Nếu có lỗi khi gọi hàm, bỏ qua kẻ địch này
                continue;
            }

            newCache.push_back(enemyData);
        }

        // Cập nhật cache với mutex
        {
            std::lock_guard<std::mutex> lock(cacheMutex);
            enemyCache = newCache;
        }
    } catch(...) {
        // Bỏ qua lỗi
    }
}

void UpdateEnemyIcons() {
    // Kiểm tra cơ bản xem ESP đã được khởi tạo và trận đấu đã bắt đầu chưa
    if (!espInitialized || !matchStarted) {
        return;
    }

    static float lastIconUpdate = 0;
    float currentTime = ImGui::GetTime();

    if(currentTime - lastIconUpdate < ICON_UPDATE_INTERVAL && !forceUpdate) {
        return;
    }

    lastIconUpdate = currentTime;

    try {
        // Kiểm tra điều kiện cơ bản
        if (!Config.ESPMenu.Enable_ESP ||
            !espManager ||
            !espManager->enemies ||
            espManager->enemies->empty()) {
            hasInitializedIcons = false;
            enemyIconTextures.clear();
            enemyNames.clear();
            return;
        }

        // Cập nhật mapping trước khi cập nhật icons
        if (!UpdateHeroMapping()) {
            return; // Nếu mapping không hợp lệ, không cập nhật icons
        }

        if(!hasInitializedIcons || forceUpdate) {
            enemyIconTextures.clear();
            enemyNames.clear();

            std::string baseImagePath = std::string("/storage/emulated/0/Android/data/") +
                                      GetPack() +
                                      "/files/TH/";

            // Lấy danh sách các heroId duy nhất từ mapping và SẮP XẾP để tránh xáo trộn
            std::vector<std::string> uniqueHeroIds;
            for (const auto& pair : objIdToHeroIdMap) {
                bool found = false;
                for (const auto& heroId : uniqueHeroIds) {
                    if (heroId == pair.second) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    uniqueHeroIds.push_back(pair.second);
                }
            }

            // SẮP XẾP để đảm bảo thứ tự nhất quán giữa các trận
            std::sort(uniqueHeroIds.begin(), uniqueHeroIds.end());

            // Tải icon cho mỗi heroId theo thứ tự đã sắp xếp
            for (const std::string& heroId : uniqueHeroIds) {
                std::string imagePath = baseImagePath + heroId + ".png";

                if(fileExists(imagePath)) {
                    GLuint textureID = LoadTexture(imagePath.c_str());
                    if(textureID != 0) {
                        enemyIconTextures.push_back(textureID);
                        try {
                            enemyNames.push_back(GetHeroName(std::stoi(heroId)));
                        } catch(...) {
                            // Nếu không thể lấy tên, thêm chuỗi trống
                            enemyNames.push_back(nullptr);
                        }
                    }
                }
            }

            hasInitializedIcons = true;
            forceUpdate = false;

            std::stringstream ss;
            ss << "Updated enemy icons with sorted order, loaded " << enemyIconTextures.size() << " icons";
            LogMessage(ss.str());
        }
    } catch(...) {
        LogMessage("Exception in UpdateEnemyIcons");
    }
}

void DrawCircleHealth(ImVec2 position, int health, int max_health, float radius, float thickness = 4.0f) {
    if (health <= 0 || max_health <= 0) {
        return; // Phòng trường hợp giá trị không hợp lệ
    }

    try {
        float healthPercent = (float)health / max_health;
        const float MY_PI = 3.14159265359f;

        ImU32 healthColor;
        if (healthPercent > 0.8f) {
            healthColor = ImColor(210, 0, 0, 255);  // Đỏ sáng
        } else if (healthPercent > 0.5f) {
            healthColor = ImColor(153, 0, 0, 255);  // Đỏ trung bình
        } else if (healthPercent > 0.3f) {
            healthColor = ImColor(102, 0, 0, 255);  // Đỏ sẫm
        } else {
            healthColor = ImColor(51, 0, 0, 255);   // Đỏ rất sẫm
        }

        // Nếu người dùng đã tùy chỉnh màu sắc trong Config, sử dụng màu từ Config
        if (Config.Color.hp_high[0] != 0.0f || Config.Color.hp_high[1] != 1.0f || Config.Color.hp_high[2] != 0.0f) {
            if (healthPercent > 0.6f) {
                healthColor = ImColor(
                    Config.Color.hp_high[0],
                    Config.Color.hp_high[1],
                    Config.Color.hp_high[2],
                    Config.Color.hp_high[3]
                );
            } else if (healthPercent > 0.3f) {
                healthColor = ImColor(
                    Config.Color.hp_mid[0],
                    Config.Color.hp_mid[1],
                    Config.Color.hp_mid[2],
                    Config.Color.hp_mid[3]
                );
            } else {
                healthColor = ImColor(
                    Config.Color.hp_low[0],
                    Config.Color.hp_low[1],
                    Config.Color.hp_low[2],
                    Config.Color.hp_low[3]
                );
            }
        }

        // Vẽ vòng máu tụt đều về hai bên
        float startAngle = MY_PI * 0.5f - (MY_PI * healthPercent);
        float endAngle = MY_PI * 0.5f + (MY_PI * healthPercent);

        ImGui::GetForegroundDrawList()->PathArcTo(position, radius, startAngle, endAngle, 32);
        ImGui::GetForegroundDrawList()->PathStroke(healthColor, ImDrawFlags_None, thickness);
    } catch(...) {
        // Bỏ qua lỗi khi vẽ
    }
}

bool isOutsideScreen(ImVec2 pos, ImVec2 screen) {
    if (pos.y < 0) {
        return true;
    }
    if (pos.x > screen.x) {
        return true;
    }
    if (pos.y > screen.y) {
        return true;
    }
    return pos.x < 0;
}

ImVec2 pushToScreenBorder(ImVec2 Pos, ImVec2 screen, int offset) {
    int x = (int) Pos.x;
    int y = (int) Pos.y;

    if (Pos.y < 0) {
        y = -offset;
    }

    if (Pos.x > screen.x) {
        x = (int) screen.x + offset;
    }

    if (Pos.y > screen.y) {
        y = (int) screen.y + offset;
    }

    if (Pos.x < 0) {
        x = -offset;
    }
    return ImVec2(x, y);
}

void Draw2DBox(ImDrawList *draw, void *instance, Vector3 EnemyPos, Camera *camera, int glHeight, int glWidth) {
    if (!draw || !instance || !camera) {
        return;
    }

    try {
        const float ASPECT_RATIO = 0.6f;
        const float HEALTH_BAR_HEIGHT = 20.0f;
        const float HEALTH_BAR_PADDING = 20.0f;
        const float CORNER_THICKNESS = 5.0f;
        const float BOX_LINE_THICKNESS = 3.0f;
        const float CORNER_LENGTH_RATIO = 0.1f;

        Vector3 head = EnemyPos + Vector3(0, 2.2f, 0);
        Vector3 foot = EnemyPos - Vector3(0, 0.2f, 0);

        Vector3 headScreen = camera->WorldToScreenPoint(head);
        Vector3 footScreen = camera->WorldToScreenPoint(foot);

        if (headScreen.z > 0 && footScreen.z > 0) {
            ImVec2 headPos = ImVec2(headScreen.x, glHeight - headScreen.y);
            ImVec2 footPos = ImVec2(footScreen.x, glHeight - footScreen.y);

            float height = abs(headPos.y - footPos.y);
            float width = height * ASPECT_RATIO;
            float centerX = (headPos.x + footPos.x) / 2;

            ImVec2 topLeft = ImVec2(centerX - width/2, headPos.y);
            ImVec2 topRight = ImVec2(centerX + width/2, headPos.y);
            ImVec2 bottomLeft = ImVec2(centerX - width/2, footPos.y);
            ImVec2 bottomRight = ImVec2(centerX + width/2, footPos.y);

            // Draw box frame
            draw->AddRect(topLeft, bottomRight, IM_COL32(255, 255, 255, 255), 0.0f, ImDrawFlags_None, BOX_LINE_THICKNESS);

            float cornerLength = height * CORNER_LENGTH_RATIO;
            ImU32 cornerColor = IM_COL32(255, 0, 0, 255);

            // Draw corners
            // Top left
            draw->AddLine(topLeft, ImVec2(topLeft.x + cornerLength, topLeft.y), cornerColor, CORNER_THICKNESS);
            draw->AddLine(topLeft, ImVec2(topLeft.x, topLeft.y + cornerLength), cornerColor, CORNER_THICKNESS);

            // Top right
            draw->AddLine(topRight, ImVec2(topRight.x - cornerLength, topRight.y), cornerColor, CORNER_THICKNESS);
            draw->AddLine(topRight, ImVec2(topRight.x, topRight.y + cornerLength), cornerColor, CORNER_THICKNESS);

            // Bottom left
            draw->AddLine(bottomLeft, ImVec2(bottomLeft.x + cornerLength, bottomLeft.y), cornerColor, CORNER_THICKNESS);
            draw->AddLine(bottomLeft, ImVec2(bottomLeft.x, bottomLeft.y - cornerLength), cornerColor, CORNER_THICKNESS);

            // Bottom right
            draw->AddLine(bottomRight, ImVec2(bottomRight.x - cornerLength, bottomRight.y), cornerColor, CORNER_THICKNESS);
            draw->AddLine(bottomRight, ImVec2(bottomRight.x, bottomRight.y - cornerLength), cornerColor, CORNER_THICKNESS);

            // Get HP info
            void *ValuePropertyComponent = *(void**)((uint64_t)instance +
                IL2Cpp::Il2CppGetFieldOffset("Project.Plugins_d.dll","NucleusDrive.Logic","LActorRoot","ValueComponent"));

            if (!ValuePropertyComponent) return;

            int currentHp = 0;
            int maxHp = 1;

            try {
                currentHp = ValuePropertyComponent_get_actorHp(ValuePropertyComponent);
                maxHp = ValuePropertyComponent_get_actorHpTotal(ValuePropertyComponent);

                // Phòng trường hợp maxHp = 0 sẽ gây lỗi chia 0
                if (maxHp <= 0) maxHp = 1;
            } catch(...) {
                // Nếu có lỗi khi lấy thông tin máu, sử dụng giá trị mặc định
                currentHp = 0;
                maxHp = 1;
            }

            float hpPercentage = (float)currentHp / maxHp;

            float healthBarWidth = width * 1.2f;
            ImVec2 healthBarPos = ImVec2(centerX - healthBarWidth/2, bottomRight.y + HEALTH_BAR_PADDING);

            ImVec2 healthBarEnd = ImVec2(healthBarPos.x + healthBarWidth, healthBarPos.y + HEALTH_BAR_HEIGHT);

            // Draw health bar background
            draw->AddRectFilled(healthBarPos, healthBarEnd, IM_COL32(51, 51, 51, 255), 10.0f);

            ImVec2 currentHealthEnd = ImVec2(healthBarPos.x + (healthBarWidth * hpPercentage), healthBarEnd.y);

            // Determine health color based on percentage
            ImU32 healthColor;
            if (hpPercentage > 0.9f) healthColor = IM_COL32(0, 255, 0, 255);
            else if (hpPercentage > 0.8f) healthColor = IM_COL32(128, 255, 0, 255);
            else if (hpPercentage > 0.7f) healthColor = IM_COL32(255, 255, 0, 255);
            else if (hpPercentage > 0.6f) healthColor = IM_COL32(255, 215, 0, 255);
            else if (hpPercentage > 0.5f) healthColor = IM_COL32(255, 165, 0, 255);
            else if (hpPercentage > 0.4f) healthColor = IM_COL32(255, 140, 0, 255);
            else if (hpPercentage > 0.3f) healthColor = IM_COL32(255, 69, 0, 255);
            else if (hpPercentage > 0.2f) healthColor = IM_COL32(255, 0, 0, 255);
            else if (hpPercentage > 0.1f) healthColor = IM_COL32(204, 0, 0, 255);
            else healthColor = IM_COL32(139, 0, 0, 255);

            // Draw current health
            draw->AddRectFilled(healthBarPos, currentHealthEnd, healthColor, 10.0f);

            // Draw health bar border
            draw->AddRect(healthBarPos, healthBarEnd, IM_COL32(255, 255, 255, 255), 10.0f, ImDrawFlags_None, 2.0f);

            // Draw health percentage
            char hpText[32];
            sprintf(hpText, "%d%%", (int)(hpPercentage * 100));
            ImVec2 textSize = ImGui::CalcTextSize(hpText);
            draw->AddText(
                ImVec2(centerX - textSize.x/2, healthBarPos.y + (HEALTH_BAR_HEIGHT - textSize.y)/2),
                IM_COL32(255, 255, 255, 255),
                hpText
            );
        }
    } catch(...) {
        // Bỏ qua lỗi khi vẽ box
    }
}

// Vẽ ESP - Hàm chính để hiển thị tất cả thông tin ESP
void DrawESP(ImDrawList *draw) {
    // Kiểm tra cơ bản trước tiên
    if (!Config.ESPMenu.Enable_ESP) {
        return;
    }

    // Kiểm tra toàn diện trước khi làm bất cứ điều gì
    if (!IsFullyReadyForESP()) {
        // Nếu ESP chưa được khởi tạo, thử khởi tạo
        if (!espInitialized) {
            InitializeESP();
        }
        return;
    }

    static float lastEspUpdate = 0;
    float currentTime = ImGui::GetTime();

    // Kiểm tra trận đấu mới
    CheckForNewMatch();

    // Nếu trận đấu chưa bắt đầu, thoát
    if (!matchStarted) {
        return;
    }

    // Limit ESP update frequency to ~60 FPS
    if(currentTime - lastEspUpdate < ESP_UPDATE_INTERVAL) {
        return;
    }

    lastEspUpdate = currentTime;

    // Cập nhật dữ liệu
    UpdateEnemyCache();
    UpdateEnemyIcons();

    // Kiểm tra espManager và enemies
    if (!espManager || !espManager->enemies || espManager->enemies->empty()) {
        return;
    }

    try {
        // Lấy thông tin người chơi
        void *actorLinker = espManager->MyPlayer;
        if (!actorLinker) {
            // Nếu không có thông tin người chơi, đánh dấu trận đấu chưa bắt đầu
            matchStarted = false;
            LogMessage("ESP stopped: MyPlayer is null");
            return;
        }

        // Kiểm tra camera với validation bổ sung
        Camera* mainCamera = Camera::get_main();
        if (!mainCamera) {
            LogMessage("ESP stopped: Main camera is null");
            return;
        }

        Vector3 myPos;
        try {
            myPos = ActorLinker_getPosition((void *)actorLinker);

            // Kiểm tra vị trí hợp lệ (không phải NaN hoặc infinity)
            if (std::isnan(myPos.x) || std::isnan(myPos.y) || std::isnan(myPos.z) ||
                std::isinf(myPos.x) || std::isinf(myPos.y) || std::isinf(myPos.z)) {
                LogMessage("ESP stopped: Invalid player position");
                return;
            }
        } catch(...) {
            LogMessage("ESP stopped: Failed to get player position");
            return;
        }

        Vector3 myPosSC = mainCamera->WorldToScreenPoint(myPos);
        ImVec2 myPos_Vec2 = ImVec2(glWidth - myPosSC.x, myPosSC.y);

        if (myPosSC.z > 0) {
            myPos_Vec2 = ImVec2(myPosSC.x, glHeight - myPosSC.y);
        }

        // Update map scale based on screen size
        if(scaleX == 1) {
            mapNgh = 3.325f;
            radiusMini = 18;
        } else if(scaleX >= 0.6f) {
            mapNgh = 2.255f;
            radiusMini = 12;
        } else {
            mapNgh = 1.835f;
            radiusMini = 9;
        }

        // Minimap handling
        MinimapSys *minimapSys = MinimapSys::get_TheMinimapSys();
        MinimapSys_CMapTransferData *mMapTransferData = NULL;

        if(minimapSys != NULL)
            mMapTransferData = minimapSys->mMapTransferData();

        std::string baseImagePath = std::string("/storage/emulated/0/Android/data/") +
                                  GetPack() +
                                  "/files/TH/";

        // Duyệt qua các kẻ địch trong cache
        std::vector<EnemyData> currentEnemies;
        {
            std::lock_guard<std::mutex> lock(cacheMutex);
            currentEnemies = enemyCache;
        }

        // Nếu không có kẻ địch trong cache, thoát
        if (currentEnemies.empty()) {
            return;
        }

        // Vẽ ESP cho từng kẻ địch
        for (const auto& enemy : currentEnemies) {
            // Bỏ qua kẻ địch đã chết
            if (enemy.isDead || enemy.hp <= 0) {
                continue;
            }

            // Kiểm tra cài đặt bỏ qua kẻ địch tàng hình
            if (Config.ESPMenu.IgnoreInvisible && !enemy.isVisible) {
                continue;
            }

            void* Enemy = enemy.enemyObj;
            void* EnemyLinker = enemy.enemyLinker;
            Vector3 EnemyPos = enemy.position;
            int objId = enemy.objId;

            // Vẽ icon trên minimap - CẢI TIẾN ĐỂ SỬA LỖI ICON XÁO TRỘN
            if(Config.ESPMenu.MinimapIcon && mMapTransferData != NULL && minimapSys != NULL) {
                // Chỉ vẽ khi đã có mapping hợp lệ và đã được xác thực
                if(mappingVerified && objIdToHeroIdMap.find(objId) != objIdToHeroIdMap.end() && !objIdToHeroIdMap[objId].empty()) {
                    std::string heroId = objIdToHeroIdMap[objId];

                    // KIỂM TRA THÊM: Đảm bảo heroId hợp lệ
                    if(!IsValidHeroId(heroId)) {
                        continue;
                    }

                    std::string imagePath = baseImagePath + heroId + ".png";

                    // Tính toán base scale dựa trên kích thước màn hình
                    float baseScale = scaleX == 1 ? 3.325f :
                                    scaleX >= 0.6f ? 2.255f : 1.835f;

                    // Tính mapNgh dựa trên loại map
                    if (minimapSys->mapType() == 1) {
                        // Mini map
                        mapNgh = baseScale * (Config.ESPMenu.MapSize / 240.0f);
                    } else {
                        // Big map
                        mapNgh = baseScale * (Config.ESPMenu.BigMapSize / 240.0f);
                    }

                    // Đảo ngược mapNgh cho team địch
                    if(ActorLinker_COM_PLAYERCAMP(EnemyLinker) == 1) {
                        mapNgh *= -1;
                    }

                    // Tính toán vị trí hiển thị trên map
                    ImVec2 minimapPos;
                    float iconScale = minimapSys->mapType() == 1 ? 1.0f : 2.0f; // Scale up for big map

                    if(minimapSys->mapType() == 1) {
                        minimapPos = ImVec2(
                            mMapTransferData->mmFinalScreenPos().x + Config.ESPMenu.MapOffsetX + (EnemyPos.x * mapNgh),
                            glHeight - mMapTransferData->mmFinalScreenPos().y + Config.ESPMenu.MapOffsetY - (EnemyPos.z * mapNgh)
                        );
                    } else {
                        minimapPos = ImVec2(
                            mMapTransferData->bmFinalScreenPos().x + (EnemyPos.x * mapNgh * mMapTransferData->bmScale().x) *
                            (ActorLinker_COM_PLAYERCAMP(EnemyLinker) == 1 ? -1 : 1),
                            glHeight - mMapTransferData->bmFinalScreenPos().y - (EnemyPos.z * mapNgh * mMapTransferData->bmScale().y) *
                            (ActorLinker_COM_PLAYERCAMP(EnemyLinker) == 1 ? -1 : 1)
                        );
                    }

                    if(fileExists(imagePath)) {
                        GLuint textureID = LoadTexture(imagePath.c_str());
                        if(textureID) {
                            float baseIconSize = Config.ESPMenu.IconSize;
                            float iconRadius = baseIconSize * iconScale;
                            float healthRadius = iconRadius + (2.0f * iconScale);
                            float iconSize = iconRadius * 1.8f;
                            // Vẽ background
                            draw->AddCircleFilled(minimapPos, iconRadius, IM_COL32(0, 0, 0, 180));

                            // Vẽ hero icon
                            ImVec2 iconMin(minimapPos.x - iconSize * 0.5f, minimapPos.y - iconSize * 0.5f);
                            ImVec2 iconMax(minimapPos.x + iconSize * 0.5f, minimapPos.y + iconSize * 0.5f);

                            draw->AddImage(
                                (void*)textureID,
                                iconMin,
                                iconMax,
                                ImVec2(0.0f, 1.0f),
                                ImVec2(1.0f, 0.0f)
                            );

                            // Lấy thông tin máu từ cache
                            float healthRatio = (float)enemy.hp / enemy.maxHp;

                            // Vẽ vòng máu background
                            draw->PathArcTo(
                                minimapPos,
                                healthRadius,
                                -3.14159f,
                                3.14159f,
                                32
                            );
                            draw->PathStroke(IM_COL32(139, 69, 19, 120), ImDrawFlags_None, 3.0f * iconScale);

                            // Vẽ vòng máu hiện tại
                            if(healthRatio > 0) {
                                float startAngle = 3.14159f * 0.5f - (3.14159f * healthRatio);
                                float endAngle = 3.14159f * 0.5f + (3.14159f * healthRatio);

                                ImU32 healthColor = healthRatio > 0.7f ? IM_COL32(200, 0, 0, 255) :
                                                   healthRatio > 0.5f ? IM_COL32(190, 0, 0, 255) :
                                                   healthRatio > 0.3f ? IM_COL32(180, 0, 0, 255) :
                                                                      IM_COL32(170, 0, 0, 255);

                                draw->PathArcTo(minimapPos, healthRadius, startAngle, endAngle, 32);
                                draw->PathStroke(healthColor, ImDrawFlags_None, 3.0f * iconScale);
                            }
                        }
                    }
                }
            }

            // Draw invisible enemy icons
            if(Config.ESPMenu.Icon && !enemy.isVisible) {
                Vector3 head = EnemyPos + Vector3(0, 2.2f, 0);
                Vector3 foot = EnemyPos - Vector3(0, 0.2f, 0);

                Vector3 headScreen = Camera::get_main()->WorldToScreenPoint(head);
                Vector3 footScreen = Camera::get_main()->WorldToScreenPoint(foot);

                if (headScreen.z > 0 && footScreen.z > 0) {
                    ImVec2 headPos = ImVec2(headScreen.x, glHeight - headScreen.y);
                    ImVec2 footPos = ImVec2(footScreen.x, glHeight - footScreen.y);

                    float height = abs(headPos.y - footPos.y);
                    float width = height * 0.6f;
                    float centerX = (headPos.x + footPos.x) / 2;

                    ImVec2 boxCenter = ImVec2(centerX, headPos.y + height/2);

                    // Chỉ vẽ khi đã có mapping hợp lệ và đã được xác thực
                    if(mappingVerified && objIdToHeroIdMap.find(objId) != objIdToHeroIdMap.end() && !objIdToHeroIdMap[objId].empty()) {
                        std::string heroId = objIdToHeroIdMap[objId];

                        // KIỂM TRA THÊM: Đảm bảo heroId hợp lệ
                        if(!IsValidHeroId(heroId)) {
                            continue;
                        }

                        std::string imagePath = baseImagePath + heroId + ".png";

                        ImVec2 uv0 = ImVec2(1.0f, 1.0f);
                        ImVec2 uv1 = ImVec2(0.0f, 0.0f);

                        if(fileExists(imagePath)) {
                            GLuint textureID = LoadTexture(imagePath.c_str());
                            if(textureID) {
                                float iconSize = width;
                                ImVec2 iconTopLeft = ImVec2(boxCenter.x - iconSize/2, boxCenter.y - iconSize/2);
                                ImVec2 iconBottomRight = ImVec2(boxCenter.x + iconSize/2, boxCenter.y + iconSize/2);

                                draw->AddImage((void*)textureID, iconTopLeft, iconBottomRight, uv0, uv1);
                                DrawCircleHealth(boxCenter, enemy.hp, enemy.maxHp, iconSize * 0.52f);
                            }
                        }
                    }
                }
            }

            // Draw player box
            if (Config.ESPMenu.PlayerBox) {
                Draw2DBox(draw, Enemy, EnemyPos, Camera::get_main(), glHeight, glWidth);
            }

            // Draw player line
            if (Config.ESPMenu.PlayerLine) {
                Vector3 headPos = EnemyPos + Vector3(0, 2.2f, 0);
                Vector3 footPos = EnemyPos - Vector3(0, 0.2f, 0);

                Vector3 headScreen = Camera::get_main()->WorldToScreenPoint(headPos);
                Vector3 footScreen = Camera::get_main()->WorldToScreenPoint(footPos);

                if (headScreen.z > 0 && footScreen.z > 0) {
                    ImVec2 screenHead(headScreen.x, glHeight - headScreen.y);
                    ImVec2 screenFoot(footScreen.x, glHeight - footScreen.y);

                    float centerX = (screenHead.x + screenFoot.x) / 2;
                    ImVec2 bottomCenter(centerX, screenFoot.y);

                    draw->AddLine(
                        myPos_Vec2,
                        bottomCenter,
                        IM_COL32(255, 0, 0, Config.Color.line[3] * 255.0f),
                        1.7f
                    );
                    draw->AddCircleFilled(myPos_Vec2, 4.0f, IM_COL32(255, 255, 255, 255));
                    draw->AddCircleFilled(bottomCenter, 4.0f, IM_COL32(255, 255, 255, 255));
                }
            }
        }
    } catch(...) {
        LogMessage("Exception in DrawESP");
    }
}

// Hook functions cần được sửa đổi để xử lý lỗi
void (*old_ActorLinker_ActorDestroy)(void *instance);
void ActorLinker_ActorDestroy(void *instance) {
    if (!instance) {
        if (old_ActorLinker_ActorDestroy) {
            old_ActorLinker_ActorDestroy(NULL);
        }
        return;
    }

    try {
        // Trước khi xóa, lấy ObjID để xóa khỏi mapping
        int objId = ActorLinker_get_ObjID(instance);

        // Đánh dấu cần cập nhật mapping
        if(objIdToHeroIdMap.find(objId) != objIdToHeroIdMap.end()) {
            std::string heroId = objIdToHeroIdMap[objId];
            if(heroIdToObjId.find(heroId) != heroIdToObjId.end() && heroIdToObjId[heroId] == objId) {
                heroIdToObjId.erase(heroId);
            }
            objIdToHeroIdMap.erase(objId);
            objIdToEnemyLinker.erase(objId);
            mappingVerified = false; // Cần cập nhật mapping khi có đối tượng bị xóa

            LogMessage("Removed mapping for objId: " + std::to_string(objId) + ", heroId: " + heroId);
        }

        old_ActorLinker_ActorDestroy(instance);

        if (ActorLinker_enemy) {
            ActorLinker_enemy->removeEnemyGivenObject(instance);
        }

        if (espManager && espManager->MyPlayer == instance) {
            espManager->MyPlayer = NULL;
        }

        // Cập nhật cache khi có đối tượng bị hủy
        UpdateEnemyCache();
    } catch(...) {
        if (old_ActorLinker_ActorDestroy) {
            old_ActorLinker_ActorDestroy(instance);
        }
        LogMessage("Exception in ActorLinker_ActorDestroy");
    }
}

void (*old_LActorRoot_ActorDestroy)(void *instance, bool bTriggerEvent);
void LActorRoot_ActorDestroy(void *instance, bool bTriggerEvent) {
    if (!instance) {
        if (old_LActorRoot_ActorDestroy) {
            old_LActorRoot_ActorDestroy(NULL, bTriggerEvent);
        }
        return;
    }

    try {
        old_LActorRoot_ActorDestroy(instance, bTriggerEvent);

        if (espManager) {
            espManager->removeEnemyGivenObject(instance);
        }

        // Cập nhật cache khi có đối tượng bị hủy
        UpdateEnemyCache();
    } catch(...) {
        if (old_LActorRoot_ActorDestroy) {
            old_LActorRoot_ActorDestroy(instance, bTriggerEvent);
        }
        LogMessage("Exception in LActorRoot_ActorDestroy");
    }
}

void (*old_ActorLinker_Update)(void *instance);
void ActorLinker_Update(void *instance) {
    if (!instance) {
        if (old_ActorLinker_Update) {
            old_ActorLinker_Update(NULL);
        }
        return;
    }

    try {
        old_ActorLinker_Update(instance);

        // Kiểm tra espManager có tồn tại không
        if (!espManager || !ActorLinker_enemy) {
            return;
        }

        if(espManager->isEnemyPresent(instance)) {
            return;
        }

        if(ActorLinker_enemy->isEnemyPresent(instance)) {
            return;
        }

        int actorType = -1;
        try {
            actorType = ActorLinker_ActorTypeDef(instance);
        } catch(...) {
            return;
        }

        if (actorType == 0) {
            bool isHostPlayer = false;

            try {
                isHostPlayer = ActorLinker_IsHostPlayer(instance);
            } catch(...) {
                return;
            }

            if (isHostPlayer) {
                espManager->tryAddMyPlayer(instance);
            } else {
                if(espManager->MyPlayer != NULL) {
                    int myPlayerCamp = 0;
                    int instanceCamp = 0;

                    try {
                        myPlayerCamp = ActorLinker_COM_PLAYERCAMP(espManager->MyPlayer);
                        instanceCamp = ActorLinker_COM_PLAYERCAMP(instance);
                    } catch(...) {
                        return;
                    }

                    if(myPlayerCamp != instanceCamp) {
                        ActorLinker_enemy->tryAddEnemy(instance);

                        // Khi thêm kẻ địch mới, cập nhật ánh xạ
                        try {
                            int objId = ActorLinker_get_ObjID(instance);
                            String* heroIcon = GetHero_Icon(instance, true);

                            if(heroIcon) {
                                std::string iconPath = heroIcon->CString();
                                if(!iconPath.empty()) {
                                    size_t lastSlash = iconPath.rfind('/');
                                    if(lastSlash != std::string::npos) {
                                        std::string heroId = iconPath.substr(lastSlash + 1).substr(2,3);

                                        if(IsValidHeroId(heroId)) {
                                            // Đánh dấu cần cập nhật mapping và icon
                                            mappingVerified = false;
                                            forceUpdate = true;

                                            // Ghi log thông tin mới
                                            std::stringstream ss;
                                            ss << "New enemy detected: objId=" << objId << ", heroId=" << heroId;
                                            LogMessage(ss.str());
                                        }
                                    }
                                }
                            }
                        } catch(...) {
                            // Bỏ qua lỗi
                        }

                        // Cập nhật cache khi có thay đổi kẻ địch
                        UpdateEnemyCache();
                    }
                }
            }
        }
    } catch(...) {
        if (old_ActorLinker_Update) {
            old_ActorLinker_Update(instance);
        }
        LogMessage("Exception in ActorLinker_Update");
    }
}

void (*old_LActorRoot_UpdateLogic)(void *instance, int delta);
void LActorRoot_UpdateLogic(void *instance, int delta) {
    if (!instance) {
        if (old_LActorRoot_UpdateLogic) {
            old_LActorRoot_UpdateLogic(NULL, delta);
        }
        return;
    }

    try {
        old_LActorRoot_UpdateLogic(instance, delta);

        if (!espManager) {
            return;
        }

        if(espManager->isEnemyPresent(instance)) {
            return;
        }

        if (espManager->MyPlayer != NULL) {
            void* heroWrapper = NULL;
            int instanceCamp = -1;
            int myPlayerCamp = -1;

            try {
                heroWrapper = (void*)LActorRoot_LHeroWrapper(instance);
                if (!heroWrapper) {
                    return;
                }

                instanceCamp = LActorRoot_COM_PLAYERCAMP(instance);
                myPlayerCamp = ActorLinker_COM_PLAYERCAMP(espManager->MyPlayer);
            } catch(...) {
                return;
            }

            if (instanceCamp == myPlayerCamp) {
                espManager->tryAddEnemy(instance);

                // Cập nhật cache khi có thay đổi kẻ địch
                UpdateEnemyCache();
            }
        }
    } catch(...) {
        if (old_LActorRoot_UpdateLogic) {
            old_LActorRoot_UpdateLogic(instance, delta);
        }
        LogMessage("Exception in LActorRoot_UpdateLogic");
    }
}

